import type { RouteRecordStringComponent } from '@vben/types';

/**
 * 该文件放非后台返回的路由 比如个人中心 等需要跳转显示的页面
 */
export const localRoutes: RouteRecordStringComponent[] = [];

/**
 * 客户列表菜单 - 第一阶段菜单（只有一级）
 */
export const customerListMenu: RouteRecordStringComponent[] = [
  {
    name: 'company-status',
    path: '/company-status',
    component: '/jsj-ai/company-status/index',
    meta: {
      order: 1,
      title: '客户列表',
      icon: 'lucide:building-2',
    },
  },
];

/**
 * 工作台菜单 - 第二阶段菜单
 */
export const workspaceMenus: RouteRecordStringComponent[] = [
  {
    component: 'BasicLayout',
    meta: {
      icon: 'lucide:file-text',
      order: 2,
      title: '原始凭证',
    },
    name: 'voucher-root',
    path: '/voucher',
    children: [
      {
        name: 'voucher-original',
        path: '/voucher/original',
        component: '/jsj-ai/voucher/original/index',
        meta: {
          icon: 'ant-design:file-image-outlined',
          title: '原始凭证',
        },
      },
    ],
  },
  {
    component: 'BasicLayout',
    meta: {
      icon: 'ant-design:file-text-outlined',
      order: 3,
      title: 'AI记账',
    },
    name: 'bookkeeping-root',
    path: '/bookkeeping',
    children: [
      {
        name: 'bookkeeping-view',
        path: '/bookkeeping/view',
        component: '/jsj-ai/voucher/bookkeeping/view/index',
        meta: {
          icon: 'ant-design:file-search-outlined',
          title: '记账凭证',
        },
      },
      {
        name: 'bookkeeping-review',
        path: '/bookkeeping/review',
        component: '/jsj-ai/voucher/bookkeeping/review/index',
        meta: {
          icon: 'ant-design:audit-outlined',
          title: '凭证审核',
          hideInMenu: true,
        },
      },
    ],
  },
  {
    component: 'BasicLayout',
    meta: {
      icon: 'ant-design:setting-outlined',
      order: 4,
      title: '配置',
    },
    name: 'configuration-root',
    path: '/configuration',
    children: [
      {
        name: 'configuration-scenario-condition',
        path: '/configuration/scenario-condition',
        component: '/jsj-ai/configuration/scenario-condition/index',
        meta: {
          icon: 'ant-design:setting-outlined',
          title: '记账场景配置',
        },
      },
      {
        name: 'configuration-scenario-entry',
        path: '/configuration/scenario-entry',
        component: '/jsj-ai/configuration/scenario-entry/index',
        meta: {
          icon: 'ant-design:form-outlined',
          title: '场景分录配置',
        },
      },
    ],
  },
];

/**
 * 简化的本地路由菜单 - 根据菜单模式动态返回
 */
export const localMenuList: RouteRecordStringComponent[] = [
  ...customerListMenu,
  ...workspaceMenus,
  ...localRoutes,
];
